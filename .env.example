# Environment variables for Wyyl<PERSON> Bot
# Copy this file to .env and fill in your actual values

# Session cookies (get these from your browser after logging in)
WYYLDE_SESSION_COOKIE=your_session_cookie_here
WYYLDE_CSRF_TOKEN=your_csrf_token_here

# Optional: Database connection (if you want to store extracted data)
DATABASE_URL=sqlite:///wyylde_data.db

# Optional: Notification settings
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=
EMAIL_SMTP_SERVER=
EMAIL_USERNAME=
EMAIL_PASSWORD=

# Optional: Proxy settings
HTTP_PROXY=
HTTPS_PROXY=

# Development settings
DEBUG=false
LOG_LEVEL=INFO
