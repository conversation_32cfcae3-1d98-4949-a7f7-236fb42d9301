#!/usr/bin/env python3
"""
Basic usage example for Wyylde Bot

This example shows how to:
1. Initialize the bot
2. Authenticate using session cookies
3. Navigate to pages
4. Extract data
"""

import os
import sys
from pathlib import Path

# Add src to path so we can import wyylde_bot
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from wyylde_bot import WyyldeBot, Config
from wyylde_bot.actions import LoginAction, NavigationAction, ExtractionAction


def main():
    """Basic usage example"""
    
    print("🤖 Wyylde Bot - Basic Usage Example")
    print("=" * 40)
    
    # Load configuration
    config = Config.load()
    
    # Initialize bot
    bot = WyyldeBot(config)
    
    # Register actions
    bot.register_action('login', LoginAction)
    bot.register_action('navigation', NavigationAction)
    bot.register_action('extraction', ExtractionAction)
    
    try:
        # Step 1: Authenticate
        print("\n1. Authenticating...")
        if bot.authenticate():
            print("✅ Authentication successful")
        else:
            print("❌ Authentication failed")
            print("Please set WYYLDE_SESSION_COOKIE in your .env file")
            return
        
        # Step 2: Navigate to profile page
        print("\n2. Navigating to profile...")
        nav_result = bot.execute_action(
            'navigation',
            action_type='goto',
            endpoint='profile'
        )
        
        if nav_result['success']:
            print(f"✅ Navigation successful: {nav_result['title']}")
        else:
            print("❌ Navigation failed")
            return
        
        # Step 3: Extract profile data
        print("\n3. Extracting profile data...")
        extraction_result = bot.execute_action(
            'extraction',
            extraction_type='css_selector',
            endpoint='profile',
            selectors={
                'username': '.username, .user-name, h1',
                'profile_info': '.profile-info, .user-info',
                'stats': '.stats, .user-stats'
            }
        )
        
        if extraction_result['success']:
            print("✅ Data extraction successful")
            print("\nExtracted data:")
            for field, data in extraction_result['data'].items():
                print(f"  {field}: {data}")
        else:
            print("❌ Data extraction failed")
        
        # Step 4: Get navigation menu
        print("\n4. Extracting navigation menu...")
        menu_result = bot.execute_action(
            'navigation',
            action_type='get_menu',
            endpoint='profile'
        )
        
        if menu_result['success']:
            print(f"✅ Found {menu_result['menu_items_count']} menu items")
            for item in menu_result['menu_items'][:5]:  # Show first 5
                print(f"  - {item['text']}: {item['absolute_url']}")
        else:
            print("❌ Menu extraction failed")
        
        print("\n✅ Example completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        
    finally:
        # Always shutdown the bot
        bot.shutdown()


if __name__ == "__main__":
    main()
