#!/usr/bin/env python3
"""
Custom extraction example for Wyylde Bot

This example shows how to create custom extraction functions
for specific data that requires complex logic.
"""

import sys
from pathlib import Path
from bs4 import BeautifulSoup
from typing import Dict, Any, List

# Add src to path so we can import wyylde_bot
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from wyylde_bot import WyyldeBot, Config
from wyylde_bot.actions import LoginAction, NavigationAction, ExtractionAction


def extract_user_profiles(soup: BeautifulSoup, response, **kwargs) -> Dict[str, Any]:
    """
    Custom function to extract user profile information
    
    Args:
        soup: BeautifulSoup object of the page
        response: HTTP response object
        **kwargs: Additional parameters
        
    Returns:
        Dict containing extracted profile data
    """
    
    profiles = []
    
    # Look for user profile cards or similar elements
    profile_elements = soup.find_all(['div', 'article'], class_=lambda x: x and any(
        keyword in x.lower() for keyword in ['profile', 'user', 'member', 'card']
    ))
    
    for element in profile_elements:
        profile_data = {}
        
        # Extract username
        username_elem = element.find(['h1', 'h2', 'h3', 'span'], class_=lambda x: x and 'name' in x.lower())
        if username_elem:
            profile_data['username'] = username_elem.get_text().strip()
        
        # Extract profile image
        img_elem = element.find('img')
        if img_elem:
            profile_data['profile_image'] = img_elem.get('src', '')
        
        # Extract age if available
        age_elem = element.find(text=lambda x: x and 'ans' in x.lower())
        if age_elem:
            # Extract number from text like "25 ans"
            import re
            age_match = re.search(r'(\d+)', age_elem)
            if age_match:
                profile_data['age'] = int(age_match.group(1))
        
        # Extract location
        location_elem = element.find(['span', 'div'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['location', 'city', 'region']
        ))
        if location_elem:
            profile_data['location'] = location_elem.get_text().strip()
        
        # Extract status (online, offline, etc.)
        status_elem = element.find(['span', 'div'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['status', 'online', 'offline']
        ))
        if status_elem:
            profile_data['status'] = status_elem.get_text().strip()
        
        # Only add if we found at least a username
        if profile_data.get('username'):
            profiles.append(profile_data)
    
    return {
        'total_profiles': len(profiles),
        'profiles': profiles,
        'page_url': response.url
    }


def extract_message_threads(soup: BeautifulSoup, response, **kwargs) -> Dict[str, Any]:
    """
    Custom function to extract message thread information
    """
    
    threads = []
    
    # Look for message thread elements
    thread_elements = soup.find_all(['div', 'li'], class_=lambda x: x and any(
        keyword in x.lower() for keyword in ['message', 'conversation', 'thread', 'chat']
    ))
    
    for element in thread_elements:
        thread_data = {}
        
        # Extract sender name
        sender_elem = element.find(['span', 'div', 'h4'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['sender', 'from', 'user', 'name']
        ))
        if sender_elem:
            thread_data['sender'] = sender_elem.get_text().strip()
        
        # Extract message preview
        preview_elem = element.find(['p', 'span', 'div'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['preview', 'excerpt', 'text', 'content']
        ))
        if preview_elem:
            thread_data['preview'] = preview_elem.get_text().strip()
        
        # Extract timestamp
        time_elem = element.find(['time', 'span'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['time', 'date', 'ago']
        ))
        if time_elem:
            thread_data['timestamp'] = time_elem.get_text().strip()
        
        # Extract unread status
        unread_elem = element.find(['span', 'div'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['unread', 'new', 'notification']
        ))
        thread_data['is_unread'] = unread_elem is not None
        
        # Only add if we found at least a sender
        if thread_data.get('sender'):
            threads.append(thread_data)
    
    return {
        'total_threads': len(threads),
        'threads': threads,
        'page_url': response.url
    }


def main():
    """Custom extraction example"""
    
    print("🤖 Wyylde Bot - Custom Extraction Example")
    print("=" * 45)
    
    # Load configuration
    config = Config.load()
    
    # Initialize bot
    bot = WyyldeBot(config)
    
    # Register actions
    bot.register_action('login', LoginAction)
    bot.register_action('navigation', NavigationAction)
    bot.register_action('extraction', ExtractionAction)
    
    try:
        # Authenticate
        print("\n1. Authenticating...")
        if not bot.authenticate():
            print("❌ Authentication failed")
            return
        print("✅ Authentication successful")
        
        # Example 1: Extract user profiles from search page
        print("\n2. Extracting user profiles...")
        profiles_result = bot.execute_action(
            'extraction',
            extraction_type='custom',
            endpoint='search',
            custom_function=extract_user_profiles,
            save_data=True,
            filename='user_profiles.json'
        )
        
        if profiles_result['success']:
            data = profiles_result['data']
            print(f"✅ Found {data['total_profiles']} user profiles")
            
            # Show first few profiles
            for i, profile in enumerate(data['profiles'][:3]):
                print(f"  Profile {i+1}:")
                for key, value in profile.items():
                    print(f"    {key}: {value}")
                print()
        
        # Example 2: Extract message threads
        print("\n3. Extracting message threads...")
        messages_result = bot.execute_action(
            'extraction',
            extraction_type='custom',
            endpoint='messages',
            custom_function=extract_message_threads,
            save_data=True,
            filename='message_threads.json'
        )
        
        if messages_result['success']:
            data = messages_result['data']
            print(f"✅ Found {data['total_threads']} message threads")
            
            # Show first few threads
            for i, thread in enumerate(data['threads'][:3]):
                print(f"  Thread {i+1}:")
                for key, value in thread.items():
                    print(f"    {key}: {value}")
                print()
        
        print("\n✅ Custom extraction example completed!")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        
    finally:
        bot.shutdown()


if __name__ == "__main__":
    main()
