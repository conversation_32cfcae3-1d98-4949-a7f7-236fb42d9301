[{"action": "navigation", "params": {"action_type": "goto", "endpoint": "profile"}, "delay": 2}, {"action": "extraction", "params": {"extraction_type": "css_selector", "endpoint": "profile", "selectors": {"username": ".username, .user-name, h1", "profile_picture": "img.profile-pic, .avatar img", "bio": ".bio, .description, .about", "stats": ".stats span, .user-stats .stat"}, "save_data": true, "filename": "profile_data.json"}}, {"action": "navigation", "params": {"action_type": "goto", "endpoint": "messages"}, "delay": 1}, {"action": "extraction", "params": {"extraction_type": "css_selector", "endpoint": "messages", "selectors": {"message_count": ".message-count, .unread-count", "recent_messages": ".message-item, .conversation-item", "message_previews": ".message-preview, .message-text"}, "save_data": true, "filename": "messages_data.json"}}, {"action": "navigation", "params": {"action_type": "get_links", "endpoint": "search", "filter_internal": true}}]