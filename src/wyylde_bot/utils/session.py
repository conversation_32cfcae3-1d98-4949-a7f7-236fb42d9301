"""
Session management utilities for Wyylde Bot
"""

import time
from typing import Dict, Optional, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .exceptions import SessionError, AuthenticationError
from ..config import Config


class SessionManager:
    """Manages HTTP sessions with cookie handling and rate limiting"""
    
    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.last_request_time = 0
        self._setup_session()
    
    def _setup_session(self) -> None:
        """Set up the requests session with retries and headers"""
        
        # Set up retry strategy
        retry_strategy = Retry(
            total=self.config.request.max_retries,
            backoff_factor=self.config.request.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update({
            'User-Agent': self.config.request.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Set session cookies if provided
        if self.config.session_cookie:
            self.session.cookies.set('session', self.config.session_cookie)
        
        if self.config.csrf_token:
            self.session.headers['X-CSRF-Token'] = self.config.csrf_token
    
    def _rate_limit(self) -> None:
        """Apply rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.config.rate_limit.delay_between_requests:
            sleep_time = self.config.rate_limit.delay_between_requests - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """Make a GET request with rate limiting"""
        self._rate_limit()
        
        try:
            response = self.session.get(
                url,
                timeout=self.config.request.timeout,
                **kwargs
            )
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            raise SessionError(f"GET request failed: {e}")
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """Make a POST request with rate limiting"""
        self._rate_limit()
        
        try:
            response = self.session.post(
                url,
                timeout=self.config.request.timeout,
                **kwargs
            )
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            raise SessionError(f"POST request failed: {e}")
    
    def verify_session(self) -> bool:
        """Verify that the session is valid by making a test request"""
        try:
            # Try to access a protected endpoint
            response = self.get(self.config.get_full_url('profile'))
            
            # Check if we're redirected to login page
            if 'login' in response.url.lower():
                return False
            
            return response.status_code == 200
        except Exception:
            return False
    
    def update_cookies(self, cookies: Dict[str, str]) -> None:
        """Update session cookies"""
        for name, value in cookies.items():
            self.session.cookies.set(name, value)
    
    def get_cookies(self) -> Dict[str, str]:
        """Get current session cookies"""
        return dict(self.session.cookies)
    
    def close(self) -> None:
        """Close the session"""
        self.session.close()
