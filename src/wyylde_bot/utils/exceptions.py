"""
Custom exceptions for Wyylde Bot
"""


class WyyldeError(Exception):
    """Base exception for Wyylde Bot"""
    pass


class AuthenticationError(WyyldeError):
    """Raised when authentication fails"""
    pass


class ExtractionError(WyyldeError):
    """Raised when data extraction fails"""
    pass


class ConfigurationError(WyyldeError):
    """Raised when configuration is invalid"""
    pass


class RateLimitError(WyyldeError):
    """Raised when rate limit is exceeded"""
    pass


class NavigationError(WyyldeError):
    """Raised when navigation fails"""
    pass


class SessionError(WyyldeError):
    """Raised when session management fails"""
    pass
