"""
Main Wyylde Bot class
"""

import logging
from typing import List, Dict, Any, Optional, Type
from pathlib import Path

from .config import Config
from .utils.session import SessionManager
from .utils.logger import setup_logger
from .utils.exceptions import WyyldeError, AuthenticationError, ConfigurationError
from .actions.base import BaseAction


class WyyldeBot:
    """Main bot class for automated Wyylde.com interactions"""
    
    def __init__(self, config: Optional[Config] = None, config_path: Optional[str] = None):
        """Initialize the bot with configuration"""
        
        # Load configuration
        if config is None:
            config = Config.load(config_path)
        self.config = config
        
        # Set up logging
        self.logger = setup_logger("wyylde_bot", self.config.logging)
        
        # Initialize session manager
        self.session_manager = SessionManager(self.config)
        
        # Track bot state
        self.is_authenticated = False
        self.consecutive_errors = 0
        self.actions_registry: Dict[str, Type[BaseAction]] = {}
        
        self.logger.info("Wyylde Bot initialized")
    
    def authenticate(self) -> bool:
        """Authenticate the bot session"""
        try:
            if not self.config.session_cookie:
                raise AuthenticationError("No session cookie provided")
            
            # Verify the session is valid
            if self.session_manager.verify_session():
                self.is_authenticated = True
                self.logger.info("Authentication successful")
                return True
            else:
                raise AuthenticationError("Session cookie is invalid or expired")
                
        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            self.is_authenticated = False
            return False
    
    def register_action(self, name: str, action_class: Type[BaseAction]) -> None:
        """Register an action class with the bot"""
        self.actions_registry[name] = action_class
        self.logger.debug(f"Registered action: {name}")
    
    def execute_action(self, action_name: str, **kwargs) -> Any:
        """Execute a registered action"""
        if not self.is_authenticated:
            raise AuthenticationError("Bot is not authenticated")
        
        if action_name not in self.actions_registry:
            raise ValueError(f"Action '{action_name}' is not registered")
        
        try:
            action_class = self.actions_registry[action_name]
            action = action_class(self.session_manager, self.config, self.logger)
            
            self.logger.info(f"Executing action: {action_name}")
            result = action.execute(**kwargs)
            
            # Reset error counter on success
            self.consecutive_errors = 0
            
            return result
            
        except Exception as e:
            self.consecutive_errors += 1
            self.logger.error(f"Action '{action_name}' failed: {e}")
            
            # Check if we've hit the error limit
            if self.consecutive_errors >= self.config.error_handling.max_consecutive_errors:
                self.logger.critical("Too many consecutive errors, stopping bot")
                raise WyyldeError("Bot stopped due to too many consecutive errors")
            
            raise
    
    def execute_workflow(self, workflow: List[Dict[str, Any]]) -> List[Any]:
        """Execute a workflow (sequence of actions)"""
        results = []
        
        for step in workflow:
            action_name = step.get('action')
            if not action_name:
                raise ValueError("Workflow step missing 'action' field")
            
            params = step.get('params', {})
            
            try:
                result = self.execute_action(action_name, **params)
                results.append(result)
                
                # Optional delay between workflow steps
                if 'delay' in step:
                    import time
                    time.sleep(step['delay'])
                    
            except Exception as e:
                self.logger.error(f"Workflow failed at step '{action_name}': {e}")
                if step.get('continue_on_error', False):
                    results.append(None)
                    continue
                else:
                    raise
        
        return results
    
    def get_status(self) -> Dict[str, Any]:
        """Get current bot status"""
        return {
            'authenticated': self.is_authenticated,
            'consecutive_errors': self.consecutive_errors,
            'registered_actions': list(self.actions_registry.keys()),
            'session_valid': self.session_manager.verify_session() if self.is_authenticated else False,
        }
    
    def shutdown(self) -> None:
        """Gracefully shutdown the bot"""
        self.logger.info("Shutting down Wyylde Bot")
        self.session_manager.close()
        self.is_authenticated = False
