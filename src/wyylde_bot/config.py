"""
Configuration management for Wyylde Bot
"""

import os
import yaml
from pathlib import Path
from typing import Any, Dict, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv


class RequestConfig(BaseModel):
    """Request configuration settings"""
    timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 1
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"


class SeleniumConfig(BaseModel):
    """Selenium configuration settings"""
    driver: str = "chrome"
    headless: bool = True
    window_size: tuple[int, int] = (1920, 1080)
    page_load_timeout: int = 30
    implicit_wait: int = 10


class LoggingConfig(BaseModel):
    """Logging configuration settings"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/wyylde_bot.log"
    max_file_size: str = "10MB"
    backup_count: int = 5


class RateLimitConfig(BaseModel):
    """Rate limiting configuration"""
    requests_per_minute: int = 60
    delay_between_requests: int = 1


class ExtractionConfig(BaseModel):
    """Data extraction configuration"""
    output_format: str = "json"
    output_directory: str = "data"
    include_metadata: bool = True


class ErrorHandlingConfig(BaseModel):
    """Error handling configuration"""
    max_consecutive_errors: int = 5
    error_cooldown: int = 300
    save_error_screenshots: bool = True


class Config(BaseModel):
    """Main configuration class for Wyylde Bot"""
    
    base_url: str = "https://wyylde.com"
    endpoints: Dict[str, str] = Field(default_factory=dict)
    request: RequestConfig = Field(default_factory=RequestConfig)
    selenium: SeleniumConfig = Field(default_factory=SeleniumConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    rate_limit: RateLimitConfig = Field(default_factory=RateLimitConfig)
    extraction: ExtractionConfig = Field(default_factory=ExtractionConfig)
    error_handling: ErrorHandlingConfig = Field(default_factory=ErrorHandlingConfig)
    
    # Environment variables
    session_cookie: Optional[str] = None
    csrf_token: Optional[str] = None
    database_url: Optional[str] = None
    
    @classmethod
    def load(cls, config_path: Optional[str] = None) -> "Config":
        """Load configuration from YAML file and environment variables"""
        
        # Load environment variables
        load_dotenv()
        
        # Default config path
        if config_path is None:
            config_path = Path(__file__).parent.parent.parent / "config" / "default.yaml"
        
        # Load YAML configuration
        config_data = {}
        if Path(config_path).exists():
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f) or {}
        
        # Override with environment variables
        env_overrides = {
            'session_cookie': os.getenv('WYYLDE_SESSION_COOKIE'),
            'csrf_token': os.getenv('WYYLDE_CSRF_TOKEN'),
            'database_url': os.getenv('DATABASE_URL'),
        }
        
        # Remove None values
        env_overrides = {k: v for k, v in env_overrides.items() if v is not None}
        config_data.update(env_overrides)
        
        return cls(**config_data)
    
    def get_full_url(self, endpoint: str) -> str:
        """Get full URL for an endpoint"""
        if endpoint.startswith('http'):
            return endpoint
        
        endpoint_path = self.endpoints.get(endpoint, endpoint)
        if not endpoint_path.startswith('/'):
            endpoint_path = '/' + endpoint_path
            
        return self.base_url.rstrip('/') + endpoint_path
