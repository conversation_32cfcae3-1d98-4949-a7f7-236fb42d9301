"""
Data extraction action for Wyylde Bot
"""

import json
import csv
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from datetime import datetime
from bs4 import BeautifulSoup, Tag

from .base import BaseAction
from ..utils.exceptions import ExtractionError


class ExtractionAction(BaseAction):
    """Action to handle data extraction from Wyylde.com pages"""
    
    @property
    def name(self) -> str:
        return "extraction"
    
    @property
    def description(self) -> str:
        return "Extract data from pages using CSS selectors or custom logic"
    
    def execute(self, extraction_type: str, **kwargs) -> Dict[str, Any]:
        """
        Execute data extraction
        
        Args:
            extraction_type: Type of extraction ('css_selector', 'table', 'form', 'custom')
            **kwargs: Additional parameters based on extraction_type
            
        Returns:
            Dict containing extracted data
        """
        self.log_action_start(extraction_type=extraction_type, **kwargs)
        
        try:
            if extraction_type == 'css_selector':
                return self._extract_by_selector(**kwargs)
            elif extraction_type == 'table':
                return self._extract_table(**kwargs)
            elif extraction_type == 'form':
                return self._extract_form(**kwargs)
            elif extraction_type == 'custom':
                return self._extract_custom(**kwargs)
            else:
                raise ValueError(f"Unknown extraction type: {extraction_type}")
                
        except Exception as e:
            self.handle_error(e, f"extraction {extraction_type}")
    
    def _extract_by_selector(self, url: str = None, endpoint: str = None,
                           selectors: Dict[str, str] = None, **kwargs) -> Dict[str, Any]:
        """Extract data using CSS selectors"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if not selectors:
            raise ValueError("'selectors' dict must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            extracted_data = {}
            
            for field_name, selector in selectors.items():
                elements = soup.select(selector)
                
                if len(elements) == 0:
                    extracted_data[field_name] = None
                elif len(elements) == 1:
                    extracted_data[field_name] = self._extract_element_data(elements[0])
                else:
                    extracted_data[field_name] = [
                        self._extract_element_data(elem) for elem in elements
                    ]
            
            result = {
                'success': True,
                'url': url,
                'extraction_type': 'css_selector',
                'data': extracted_data,
                'timestamp': datetime.now().isoformat()
            }
            
            # Save data if requested
            if kwargs.get('save_data'):
                self._save_extracted_data(result, kwargs.get('filename'))
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise ExtractionError(f"Failed to extract data from {url}: {e}")
    
    def _extract_table(self, url: str = None, endpoint: str = None,
                      table_selector: str = 'table', **kwargs) -> Dict[str, Any]:
        """Extract data from HTML tables"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            tables = soup.select(table_selector)
            if not tables:
                raise ExtractionError(f"No tables found with selector: {table_selector}")
            
            extracted_tables = []
            
            for i, table in enumerate(tables):
                table_data = {
                    'table_index': i,
                    'headers': [],
                    'rows': []
                }
                
                # Extract headers
                header_row = table.find('tr')
                if header_row:
                    headers = header_row.find_all(['th', 'td'])
                    table_data['headers'] = [h.get_text().strip() for h in headers]
                
                # Extract data rows
                rows = table.find_all('tr')[1:]  # Skip header row
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    row_data = [cell.get_text().strip() for cell in cells]
                    table_data['rows'].append(row_data)
                
                extracted_tables.append(table_data)
            
            result = {
                'success': True,
                'url': url,
                'extraction_type': 'table',
                'tables_count': len(extracted_tables),
                'data': extracted_tables,
                'timestamp': datetime.now().isoformat()
            }
            
            # Save data if requested
            if kwargs.get('save_data'):
                self._save_extracted_data(result, kwargs.get('filename'))
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise ExtractionError(f"Failed to extract table data from {url}: {e}")
    
    def _extract_form(self, url: str = None, endpoint: str = None,
                     form_selector: str = 'form', **kwargs) -> Dict[str, Any]:
        """Extract form structure and data"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            forms = soup.select(form_selector)
            if not forms:
                raise ExtractionError(f"No forms found with selector: {form_selector}")
            
            extracted_forms = []
            
            for i, form in enumerate(forms):
                form_data = {
                    'form_index': i,
                    'action': form.get('action', ''),
                    'method': form.get('method', 'GET').upper(),
                    'fields': []
                }
                
                # Extract form fields
                for field in form.find_all(['input', 'select', 'textarea']):
                    field_info = {
                        'tag': field.name,
                        'type': field.get('type', ''),
                        'name': field.get('name', ''),
                        'id': field.get('id', ''),
                        'value': field.get('value', ''),
                        'required': field.has_attr('required'),
                        'placeholder': field.get('placeholder', '')
                    }
                    
                    # Handle select options
                    if field.name == 'select':
                        options = []
                        for option in field.find_all('option'):
                            options.append({
                                'value': option.get('value', ''),
                                'text': option.get_text().strip(),
                                'selected': option.has_attr('selected')
                            })
                        field_info['options'] = options
                    
                    form_data['fields'].append(field_info)
                
                extracted_forms.append(form_data)
            
            result = {
                'success': True,
                'url': url,
                'extraction_type': 'form',
                'forms_count': len(extracted_forms),
                'data': extracted_forms,
                'timestamp': datetime.now().isoformat()
            }
            
            # Save data if requested
            if kwargs.get('save_data'):
                self._save_extracted_data(result, kwargs.get('filename'))
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise ExtractionError(f"Failed to extract form data from {url}: {e}")
    
    def _extract_custom(self, url: str = None, endpoint: str = None,
                       custom_function: callable = None, **kwargs) -> Dict[str, Any]:
        """Extract data using a custom function"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if not custom_function:
            raise ValueError("'custom_function' must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Call custom extraction function
            extracted_data = custom_function(soup, response, **kwargs)
            
            result = {
                'success': True,
                'url': url,
                'extraction_type': 'custom',
                'data': extracted_data,
                'timestamp': datetime.now().isoformat()
            }
            
            # Save data if requested
            if kwargs.get('save_data'):
                self._save_extracted_data(result, kwargs.get('filename'))
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise ExtractionError(f"Failed to extract custom data from {url}: {e}")
    
    def _extract_element_data(self, element: Tag) -> Union[str, Dict[str, Any]]:
        """Extract data from a single element"""
        
        # Get text content
        text = element.get_text().strip()
        
        # Get attributes
        attrs = dict(element.attrs) if element.attrs else {}
        
        # Return just text if no attributes, otherwise return dict
        if not attrs:
            return text
        else:
            return {
                'text': text,
                'attributes': attrs,
                'tag': element.name
            }
    
    def _save_extracted_data(self, data: Dict[str, Any], filename: str = None) -> None:
        """Save extracted data to file"""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"extracted_data_{timestamp}.json"
        
        output_dir = Path(self.config.extraction.output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / filename
        
        # Determine format from filename extension
        if filename.endswith('.json'):
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        elif filename.endswith('.csv'):
            # For CSV, we need to flatten the data structure
            self._save_as_csv(data, output_path)
        else:
            # Default to JSON
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Extracted data saved to: {output_path}")
    
    def _save_as_csv(self, data: Dict[str, Any], output_path: Path) -> None:
        """Save data as CSV (simplified version)"""
        
        # This is a basic implementation - you might need to customize
        # based on your specific data structure
        
        extracted_data = data.get('data', {})
        
        if isinstance(extracted_data, dict):
            # Convert dict to list of rows
            rows = []
            for key, value in extracted_data.items():
                if isinstance(value, list):
                    for item in value:
                        rows.append({'field': key, 'value': str(item)})
                else:
                    rows.append({'field': key, 'value': str(value)})
            
            if rows:
                with open(output_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=['field', 'value'])
                    writer.writeheader()
                    writer.writerows(rows)
