"""
Navigation action for Wyylde Bot
"""

from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from .base import BaseAction
from ..utils.exceptions import NavigationError


class NavigationAction(BaseAction):
    """Action to handle navigation on Wyylde.com"""
    
    @property
    def name(self) -> str:
        return "navigation"
    
    @property
    def description(self) -> str:
        return "Navigate to pages and extract navigation information"
    
    def execute(self, action_type: str, **kwargs) -> Dict[str, Any]:
        """
        Execute navigation action
        
        Args:
            action_type: Type of navigation ('goto', 'get_links', 'get_menu', 'breadcrumb')
            **kwargs: Additional parameters based on action_type
            
        Returns:
            Dict containing navigation results
        """
        self.log_action_start(action_type=action_type, **kwargs)
        
        try:
            if action_type == 'goto':
                return self._goto_page(**kwargs)
            elif action_type == 'get_links':
                return self._get_links(**kwargs)
            elif action_type == 'get_menu':
                return self._get_menu(**kwargs)
            elif action_type == 'breadcrumb':
                return self._get_breadcrumb(**kwargs)
            else:
                raise ValueError(f"Unknown navigation action type: {action_type}")
                
        except Exception as e:
            self.handle_error(e, f"navigation {action_type}")
    
    def _goto_page(self, url: str = None, endpoint: str = None, **kwargs) -> Dict[str, Any]:
        """Navigate to a specific page"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract page information
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "No title"
            
            # Check if we were redirected
            final_url = response.url
            was_redirected = final_url != url
            
            result = {
                'success': True,
                'url': url,
                'final_url': final_url,
                'was_redirected': was_redirected,
                'status_code': response.status_code,
                'title': title_text,
                'content_length': len(response.content)
            }
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise NavigationError(f"Failed to navigate to {url}: {e}")
    
    def _get_links(self, url: str = None, endpoint: str = None, 
                   filter_internal: bool = True, **kwargs) -> Dict[str, Any]:
        """Extract all links from a page"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            links = []
            base_domain = urlparse(self.config.base_url).netloc
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                text = link.get_text().strip()
                
                # Convert relative URLs to absolute
                absolute_url = urljoin(url, href)
                
                # Filter internal links if requested
                if filter_internal:
                    link_domain = urlparse(absolute_url).netloc
                    if link_domain and link_domain != base_domain:
                        continue
                
                links.append({
                    'text': text,
                    'href': href,
                    'absolute_url': absolute_url,
                    'is_internal': urlparse(absolute_url).netloc == base_domain
                })
            
            result = {
                'success': True,
                'url': url,
                'links_count': len(links),
                'links': links
            }
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise NavigationError(f"Failed to extract links from {url}: {e}")
    
    def _get_menu(self, url: str = None, endpoint: str = None, 
                  menu_selector: str = None, **kwargs) -> Dict[str, Any]:
        """Extract navigation menu from a page"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Try different common menu selectors
            menu_selectors = [
                menu_selector,
                'nav',
                '.navigation',
                '.nav',
                '.menu',
                '#navigation',
                '#nav',
                '#menu'
            ]
            
            menu_items = []
            menu_found = False
            
            for selector in menu_selectors:
                if not selector:
                    continue
                    
                menu = soup.select_one(selector)
                if menu:
                    menu_found = True
                    for link in menu.find_all('a', href=True):
                        text = link.get_text().strip()
                        href = link['href']
                        absolute_url = urljoin(url, href)
                        
                        menu_items.append({
                            'text': text,
                            'href': href,
                            'absolute_url': absolute_url
                        })
                    break
            
            result = {
                'success': menu_found,
                'url': url,
                'menu_items_count': len(menu_items),
                'menu_items': menu_items
            }
            
            if not menu_found:
                result['message'] = 'No navigation menu found'
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise NavigationError(f"Failed to extract menu from {url}: {e}")
    
    def _get_breadcrumb(self, url: str = None, endpoint: str = None, **kwargs) -> Dict[str, Any]:
        """Extract breadcrumb navigation from a page"""
        
        if not url and not endpoint:
            raise ValueError("Either 'url' or 'endpoint' must be provided")
        
        if endpoint:
            url = self.config.get_full_url(endpoint)
        
        try:
            response = self.session_manager.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Try different breadcrumb selectors
            breadcrumb_selectors = [
                '.breadcrumb',
                '.breadcrumbs',
                '[aria-label="breadcrumb"]',
                '.nav-breadcrumb'
            ]
            
            breadcrumb_items = []
            breadcrumb_found = False
            
            for selector in breadcrumb_selectors:
                breadcrumb = soup.select_one(selector)
                if breadcrumb:
                    breadcrumb_found = True
                    for link in breadcrumb.find_all(['a', 'span']):
                        text = link.get_text().strip()
                        href = link.get('href', '')
                        
                        if href:
                            absolute_url = urljoin(url, href)
                        else:
                            absolute_url = ''
                        
                        breadcrumb_items.append({
                            'text': text,
                            'href': href,
                            'absolute_url': absolute_url,
                            'is_current': not bool(href)
                        })
                    break
            
            result = {
                'success': breadcrumb_found,
                'url': url,
                'breadcrumb_items': breadcrumb_items
            }
            
            if not breadcrumb_found:
                result['message'] = 'No breadcrumb navigation found'
            
            self.log_action_end(result)
            return result
            
        except Exception as e:
            raise NavigationError(f"Failed to extract breadcrumb from {url}: {e}")
