"""
Login action for Wyylde Bot
"""

from typing import Dict, Any
from bs4 import BeautifulSoup

from .base import BaseAction
from ..utils.exceptions import AuthenticationError


class LoginAction(BaseAction):
    """Action to handle login to Wyylde.com"""
    
    @property
    def name(self) -> str:
        return "login"
    
    @property
    def description(self) -> str:
        return "Login to Wyylde.com using credentials"
    
    def execute(self, username: str = None, password: str = None, **kwargs) -> Dict[str, Any]:
        """
        Execute login action
        
        Args:
            username: Login username/email
            password: Login password
            
        Returns:
            Dict containing login status and session info
        """
        self.log_action_start(username=username)
        
        try:
            # If we already have a valid session cookie, verify it
            if self.config.session_cookie:
                if self.session_manager.verify_session():
                    self.log_action_end()
                    return {
                        'success': True,
                        'method': 'session_cookie',
                        'message': 'Already authenticated with session cookie'
                    }
            
            # If credentials provided, attempt login
            if username and password:
                return self._login_with_credentials(username, password)
            
            # No valid authentication method
            raise AuthenticationError("No valid authentication method available")
            
        except Exception as e:
            self.handle_error(e, "login execution")
    
    def _login_with_credentials(self, username: str, password: str) -> Dict[str, Any]:
        """Login using username and password"""
        
        try:
            # Get login page to extract CSRF token and form data
            login_url = self.config.get_full_url('login')
            response = self.session_manager.get(login_url)
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find login form
            login_form = soup.find('form', {'id': 'login-form'}) or soup.find('form')
            if not login_form:
                raise AuthenticationError("Could not find login form")
            
            # Extract form data
            form_data = {}
            for input_field in login_form.find_all('input'):
                name = input_field.get('name')
                value = input_field.get('value', '')
                if name:
                    form_data[name] = value
            
            # Update with credentials
            form_data.update({
                'username': username,
                'email': username,  # Some sites use email field
                'password': password,
            })
            
            # Submit login form
            form_action = login_form.get('action', login_url)
            if not form_action.startswith('http'):
                form_action = self.config.base_url.rstrip('/') + '/' + form_action.lstrip('/')
            
            response = self.session_manager.post(form_action, data=form_data)
            
            # Check if login was successful
            if self._check_login_success(response):
                # Update session cookies
                cookies = self.session_manager.get_cookies()
                
                self.log_action_end()
                return {
                    'success': True,
                    'method': 'credentials',
                    'message': 'Login successful',
                    'cookies': cookies
                }
            else:
                raise AuthenticationError("Login failed - invalid credentials or form submission")
                
        except Exception as e:
            raise AuthenticationError(f"Credential login failed: {e}")
    
    def _check_login_success(self, response) -> bool:
        """Check if login was successful based on response"""
        
        # Check if redirected to login page (indicates failure)
        if 'login' in response.url.lower():
            return False
        
        # Check for common success indicators
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for logout link or user menu (indicates success)
        if soup.find('a', href=lambda x: x and 'logout' in x.lower()):
            return True
        
        if soup.find('div', class_=lambda x: x and 'user-menu' in x.lower()):
            return True
        
        # Look for error messages (indicates failure)
        error_indicators = [
            'error', 'invalid', 'incorrect', 'failed', 'wrong'
        ]
        
        for indicator in error_indicators:
            if soup.find(text=lambda x: x and indicator in x.lower()):
                return False
        
        # If we can access a protected endpoint, login was successful
        try:
            profile_response = self.session_manager.get(self.config.get_full_url('profile'))
            return profile_response.status_code == 200 and 'login' not in profile_response.url.lower()
        except:
            return False
