"""
Base action class for Wyylde Bot actions
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from ..config import Config
from ..utils.session import SessionManager


class BaseAction(ABC):
    """Abstract base class for all bot actions"""
    
    def __init__(self, session_manager: SessionManager, config: Config, logger: logging.Logger):
        self.session_manager = session_manager
        self.config = config
        self.logger = logger
    
    @abstractmethod
    def execute(self, **kwargs) -> Any:
        """Execute the action with given parameters"""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Return the action name"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Return the action description"""
        pass
    
    def validate_params(self, required_params: list, **kwargs) -> None:
        """Validate that required parameters are provided"""
        missing_params = [param for param in required_params if param not in kwargs]
        if missing_params:
            raise ValueError(f"Missing required parameters: {missing_params}")
    
    def log_action_start(self, **kwargs) -> None:
        """Log the start of an action"""
        params_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
        self.logger.info(f"Starting {self.name} action with params: {params_str}")
    
    def log_action_end(self, result: Any = None) -> None:
        """Log the end of an action"""
        self.logger.info(f"Completed {self.name} action")
        if result is not None:
            self.logger.debug(f"Action result: {result}")
    
    def handle_error(self, error: Exception, context: str = "") -> None:
        """Handle and log errors consistently"""
        error_msg = f"Error in {self.name} action"
        if context:
            error_msg += f" ({context})"
        error_msg += f": {error}"
        
        self.logger.error(error_msg)
        raise
