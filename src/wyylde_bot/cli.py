"""
Command Line Interface for Wyylde Bot
"""

import click
import json
from pathlib import Path
from typing import Dict, Any

from .bot import <PERSON>yyldeBot
from .config import Config
from .actions import LoginAction, NavigationAction, ExtractionAction


@click.group()
@click.option('--config', '-c', help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def main(ctx, config, verbose):
    """Wyylde Bot - Automated extraction bot for wyylde.com"""
    
    # Load configuration
    bot_config = Config.load(config)
    if verbose:
        bot_config.logging.level = "DEBUG"
    
    # Initialize bot
    bot = WyyldeBot(bot_config)
    
    # Register actions
    bot.register_action('login', LoginAction)
    bot.register_action('navigation', NavigationAction)
    bot.register_action('extraction', ExtractionAction)
    
    # Store bot in context
    ctx.ensure_object(dict)
    ctx.obj['bot'] = bot


@main.command()
@click.option('--username', '-u', help='Username for login')
@click.option('--password', '-p', help='Password for login')
@click.pass_context
def login(ctx, username, password):
    """Login to Wyylde.com"""
    
    bot: WyyldeBot = ctx.obj['bot']
    
    try:
        if bot.authenticate():
            click.echo("✅ Authentication successful using session cookie")
        elif username and password:
            result = bot.execute_action('login', username=username, password=password)
            if result['success']:
                click.echo("✅ Login successful")
                click.echo(f"Method: {result['method']}")
            else:
                click.echo("❌ Login failed")
        else:
            click.echo("❌ No valid authentication method available")
            click.echo("Please provide session cookie in config or use --username/--password")
            
    except Exception as e:
        click.echo(f"❌ Error: {e}")
    finally:
        bot.shutdown()


@main.command()
@click.option('--url', help='URL to navigate to')
@click.option('--endpoint', help='Endpoint name from config')
@click.pass_context
def goto(ctx, url, endpoint):
    """Navigate to a page"""
    
    bot: WyyldeBot = ctx.obj['bot']
    
    try:
        if not bot.authenticate():
            click.echo("❌ Authentication failed")
            return
        
        result = bot.execute_action('navigation', action_type='goto', url=url, endpoint=endpoint)
        
        if result['success']:
            click.echo(f"✅ Navigation successful")
            click.echo(f"Title: {result['title']}")
            click.echo(f"Final URL: {result['final_url']}")
            if result['was_redirected']:
                click.echo("⚠️  Page was redirected")
        else:
            click.echo("❌ Navigation failed")
            
    except Exception as e:
        click.echo(f"❌ Error: {e}")
    finally:
        bot.shutdown()


@main.command()
@click.option('--url', help='URL to extract from')
@click.option('--endpoint', help='Endpoint name from config')
@click.option('--selectors', help='JSON string of CSS selectors')
@click.option('--save', is_flag=True, help='Save extracted data to file')
@click.option('--filename', help='Output filename')
@click.pass_context
def extract(ctx, url, endpoint, selectors, save, filename):
    """Extract data from a page"""
    
    bot: WyyldeBot = ctx.obj['bot']
    
    try:
        if not bot.authenticate():
            click.echo("❌ Authentication failed")
            return
        
        if selectors:
            selectors_dict = json.loads(selectors)
        else:
            # Default selectors for common elements
            selectors_dict = {
                'title': 'h1',
                'content': '.content, .main, article',
                'links': 'a[href]'
            }
        
        result = bot.execute_action(
            'extraction',
            extraction_type='css_selector',
            url=url,
            endpoint=endpoint,
            selectors=selectors_dict,
            save_data=save,
            filename=filename
        )
        
        if result['success']:
            click.echo("✅ Data extraction successful")
            click.echo(f"URL: {result['url']}")
            click.echo(f"Timestamp: {result['timestamp']}")
            
            # Display extracted data
            for field, data in result['data'].items():
                click.echo(f"\n{field}:")
                if isinstance(data, list):
                    for i, item in enumerate(data[:3]):  # Show first 3 items
                        click.echo(f"  {i+1}. {str(item)[:100]}...")
                    if len(data) > 3:
                        click.echo(f"  ... and {len(data) - 3} more items")
                else:
                    click.echo(f"  {str(data)[:200]}...")
        else:
            click.echo("❌ Data extraction failed")
            
    except Exception as e:
        click.echo(f"❌ Error: {e}")
    finally:
        bot.shutdown()


@main.command()
@click.option('--workflow-file', '-w', required=True, help='Path to workflow JSON file')
@click.pass_context
def workflow(ctx, workflow_file):
    """Execute a workflow from JSON file"""
    
    bot: WyyldeBot = ctx.obj['bot']
    
    try:
        if not bot.authenticate():
            click.echo("❌ Authentication failed")
            return
        
        # Load workflow
        with open(workflow_file, 'r') as f:
            workflow_data = json.load(f)
        
        click.echo(f"🚀 Starting workflow with {len(workflow_data)} steps")
        
        results = bot.execute_workflow(workflow_data)
        
        click.echo("✅ Workflow completed successfully")
        click.echo(f"Results: {len(results)} steps executed")
        
    except Exception as e:
        click.echo(f"❌ Error: {e}")
    finally:
        bot.shutdown()


@main.command()
@click.pass_context
def status(ctx):
    """Show bot status"""
    
    bot: WyyldeBot = ctx.obj['bot']
    
    try:
        status_info = bot.get_status()
        
        click.echo("🤖 Wyylde Bot Status")
        click.echo(f"Authenticated: {'✅' if status_info['authenticated'] else '❌'}")
        click.echo(f"Session Valid: {'✅' if status_info['session_valid'] else '❌'}")
        click.echo(f"Consecutive Errors: {status_info['consecutive_errors']}")
        click.echo(f"Registered Actions: {', '.join(status_info['registered_actions'])}")
        
    except Exception as e:
        click.echo(f"❌ Error: {e}")
    finally:
        bot.shutdown()


if __name__ == '__main__':
    main()
