# Default configuration for Wyylde Bot

# Base URL and endpoints
base_url: "https://wyylde.com"
endpoints:
  login: "/login"
  profile: "/profile"
  messages: "/messages"
  search: "/search"

# Request settings
request:
  timeout: 30
  max_retries: 3
  retry_delay: 1
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# Selenium settings (if needed)
selenium:
  driver: "chrome"  # chrome, firefox, edge
  headless: true
  window_size: [1920, 1080]
  page_load_timeout: 30
  implicit_wait: 10

# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/wyylde_bot.log"
  max_file_size: "10MB"
  backup_count: 5

# Rate limiting
rate_limit:
  requests_per_minute: 60
  delay_between_requests: 1

# Data extraction settings
extraction:
  output_format: "json"  # json, csv, xml
  output_directory: "data"
  include_metadata: true
  
# Error handling
error_handling:
  max_consecutive_errors: 5
  error_cooldown: 300  # seconds
  save_error_screenshots: true
