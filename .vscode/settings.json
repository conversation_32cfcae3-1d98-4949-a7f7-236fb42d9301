{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "__pycache__": true, "_site": true, ".asset-cache": true, ".bundle": true, ".ipynb_checkpoints": true, ".pytest_cache": true, ".sass-cache": true, ".svn": true, "**/.egg-info": true, "build": true, "coverage": true, "dist": true, "log": true, "node_modules": true, "public/packs": true, "tmp": true, "**/.retool_types/**": true, "**/*tsconfig.json": true, ".cache": true, "retool.config.json": true}}